import { useMessageGetter } from '@messageformat/react';
import { ChevronRightIcon } from '@shape-construction/arch-ui/src/Icons/outline';

type ChangeSignalsWidgetProps = {
    onBrowseClick: () => void;
};
export const ChangeSignalsWidget: React.FC<ChangeSignalsWidgetProps> = ({ onBrowseClick }) => {
    const messages = useMessageGetter('controlCenter.changeSignals');
    return (
        <div className="flex items-stretch bg-brand-subtlest border border-brand-subtlest rounded-sm">
            <div className="flex items-center px-4 py-2">
                <span className="text-xs font-medium text-black">
                    {messages('title')}
                </span>
            </div>

            <div className="w-p border-r border-brand-subtlest" />

            <div className="flex items-center px-4 py-2">
                <button
                    aria-label={messages('changeSignalsCTA')}
                    onClick={onBrowseClick}
                    type="button"
                    className="flex gap-1 items-center text-xs font-medium text-brand"
                >
                    {messages('changeSignalsCTA')}
                    <ChevronRightIcon width={16} height={16} />
                </button>
            </div>
        </div>
    );
};
